<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>About <PERSON> - MERN Stack Developer & AI Automation Expert</title>
    <meta name="description" content="Learn about <PERSON>'s background, expertise in MERN stack development, AI automation, and passion for creating innovative solutions." />
    <link rel="stylesheet" href="main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
</head>
<body class="bg-background text-text-primary">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <nav class="container-max section-padding py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">TV</span>
                    </div>
                    <span class="text-xl font-semibold"><PERSON> van <PERSON></span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="about.html" class="text-accent font-medium">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary">Get In Touch</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-surface transition-smooth" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Menu -->
            <div class="md:hidden mt-4 pb-4 border-t border-border pt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="about.html" class="text-accent font-medium">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary w-full text-center">Get In Touch</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-background via-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    About <span class="text-gradient">Thomas van Rossum</span>
                </h1>
                <p class="text-xl text-text-secondary mb-8 leading-relaxed">
                    Passionate MERN stack developer and AI automation specialist with a mission to transform ideas into innovative digital solutions.
                </p>
            </div>
        </div>
    </section>

    <!-- About Content -->
    <section class="section-padding">
        <div class="container-max">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
                <div class="order-2 lg:order-1">
                    <h2 class="text-3xl font-bold mb-6">My Journey</h2>
                    <p class="text-text-secondary mb-6 leading-relaxed">
                        I am a passionate and results-driven developer with a strong background in building robust web applications using the MERN stack (MongoDB, Express.js, React, Node.js). My expertise extends to creating AI-powered automations that streamline processes and enhance efficiency.
                    </p>
                    <p class="text-text-secondary mb-6 leading-relaxed">
                        I thrive on tackling complex challenges and delivering innovative solutions that meet and exceed client expectations. With years of experience in modern web development, I've helped businesses of all sizes transform their digital presence and automate their workflows.
                    </p>
                    <p class="text-text-secondary mb-6 leading-relaxed">
                        My approach combines technical excellence with a deep understanding of business needs, ensuring that every solution I create not only works flawlessly but also drives real value for my clients.
                    </p>
                </div>
                <div class="order-1 lg:order-2 flex justify-center">
                    <div class="w-80 h-80 lg:w-96 lg:h-96 rounded-lg overflow-hidden shadow-dramatic">
                        <img src="images/LogoNew.png" alt="Thomas van Rossum" class="w-full h-full object-cover rounded-full" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills & Expertise -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Skills & Expertise</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Frontend Development -->
                <div class="card hover:shadow-hover transition-smooth">
                    <div class="w-16 h-16 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Frontend Development</h3>
                    <p class="text-text-secondary mb-4">
                        Expert in React, Next.js, and modern JavaScript. Creating responsive, performant user interfaces with exceptional user experiences.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React</span>
                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Next.js</span>
                        <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">JavaScript</span>
                        <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">TypeScript</span>
                    </div>
                </div>

                <!-- Backend Development -->
                <div class="card hover:shadow-hover transition-smooth">
                    <div class="w-16 h-16 bg-green-500/20 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Backend Development</h3>
                    <p class="text-text-secondary mb-4">
                        Building scalable server-side applications with Node.js, Express, and robust database architectures for optimal performance.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                        <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Express</span>
                        <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">PostgreSQL</span>
                    </div>
                </div>

                <!-- AI & Automation -->
                <div class="card hover:shadow-hover transition-smooth">
                    <div class="w-16 h-16 bg-red-500/20 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">AI & Automation</h3>
                    <p class="text-text-secondary mb-4">
                        Implementing intelligent automation solutions using AI/ML technologies to streamline business processes and enhance efficiency.
                    </p>
                    <div class="flex flex-wrap gap-2">
                        <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">OpenAI</span>
                        <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">TensorFlow</span>
                        <span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs">FastAPI</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Experience & Achievements -->
    <section class="section-padding">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Experience & Achievements</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="text-4xl font-bold text-accent mb-2">5+</div>
                    <div class="text-text-secondary">Years Experience</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-success mb-2">50+</div>
                    <div class="text-text-secondary">Projects Completed</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-warning mb-2">98%</div>
                    <div class="text-text-secondary">Client Satisfaction</div>
                </div>
                <div class="text-center">
                    <div class="text-4xl font-bold text-accent mb-2">24/7</div>
                    <div class="text-text-secondary">Support Available</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Values & Approach -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">My Approach</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-accent/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Innovation First</h3>
                    <p class="text-text-secondary">
                        Always staying ahead of the curve with the latest technologies and best practices to deliver cutting-edge solutions.
                    </p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-success/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Quality Focused</h3>
                    <p class="text-text-secondary">
                        Committed to delivering high-quality, well-tested, and maintainable code that stands the test of time.
                    </p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-warning/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Client Partnership</h3>
                    <p class="text-text-secondary">
                        Building long-term relationships through clear communication, transparency, and collaborative problem-solving.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-to-br from-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-4xl font-bold mb-6">Let's Work Together</h2>
                <p class="text-xl text-text-secondary mb-8">
                    Ready to bring your ideas to life? I'd love to discuss your project and explore how we can create something amazing together.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.html" class="btn-primary">
                        Start a Conversation
                    </a>
                    <a href="projects.html" class="btn-secondary">
                        View My Work
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary border-t border-border">
        <div class="container-max section-padding py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">TV</span>
                        </div>
                        <span class="text-lg font-semibold">Thomas van Rossum</span>
                    </div>
                    <p class="text-text-secondary mb-4">
                        MERN Stack Developer & AI Automation Specialist creating innovative solutions for modern businesses.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Navigation</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="index.html" class="hover:text-accent transition-smooth">Home</a></li>
                        <li><a href="about.html" class="hover:text-accent transition-smooth">About</a></li>
                        <li><a href="projects.html" class="hover:text-accent transition-smooth">Projects</a></li>
                        <li><a href="blog.html" class="hover:text-accent transition-smooth">Blog</a></li>
                        <li><a href="contact.html" class="hover:text-accent transition-smooth">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Services</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="#" class="hover:text-accent transition-smooth">MERN Development</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">AI Automation</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Consulting</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Technical Audits</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-border mt-8 pt-8 text-center text-text-secondary">
                <p>&copy; 2025 Thomas van Rossum. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>
