<html>
  <head>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="" />
    <link
      rel="stylesheet"
      as="style"
      onload="this.rel='stylesheet'"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
    />

    <title>Stitch Design</title>
    <link rel="icon" type="image/x-icon" href="data:image/x-icon;base64," />

    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
  </head>
  <body>
    <div
      class="relative flex size-full min-h-screen flex-col bg-[#14191f] dark group/design-root overflow-x-hidden"
      style='font-family: Inter, "Noto Sans", sans-serif;'
    >
      <div class="layout-container flex h-full grow flex-col">
        <header
          class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#293542] px-10 py-3"
        >
          <div class="flex items-center gap-4 text-white">
            <div class="size-4">
              <svg
                viewBox="0 0 48 48"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M24 4H42V17.3333V30.6667H24V44H6V30.6667V17.3333H24V4Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <a href="index.html" class="text-white text-lg font-bold leading-tight tracking-[-0.015em] hover:underline">  Thomas van Rossum</a> 
          </div>
          <div class="flex flex-1 justify-end gap-8">
            <div class="flex items-center gap-9">
              <a class="text-white text-sm font-medium leading-normal" href="About.html">About</a>
              <a class="text-white text-sm font-medium leading-normal" href="Projects.html">Projects</a>
          <a class="text-white text-sm font-medium leading-normal" href="Contact.html">Contact</a>
              >
            </div>
            <div class="flex gap-2">
              <button
                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#293542] text-white gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
              >
                <div
                  class="text-white"
                  data-icon="GithubLogo"
                  data-size="20px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M208.31,75.68A59.78,59.78,0,0,0,202.93,28,8,8,0,0,0,196,24a59.75,59.75,0,0,0-48,24H124A59.75,59.75,0,0,0,76,24a8,8,0,0,0-6.93,4,59.78,59.78,0,0,0-5.38,47.68A58.14,58.14,0,0,0,56,104v8a56.06,56.06,0,0,0,48.44,55.47A39.8,39.8,0,0,0,96,192v8H72a24,24,0,0,1-24-24A40,40,0,0,0,8,136a8,8,0,0,0,0,16,24,24,0,0,1,24,24,40,40,0,0,0,40,40H96v16a8,8,0,0,0,16,0V192a24,24,0,0,1,48,0v40a8,8,0,0,0,16,0V192a39.8,39.8,0,0,0-8.44-24.53A56.06,56.06,0,0,0,216,112v-8A58.14,58.14,0,0,0,208.31,75.68ZM200,112a40,40,0,0,1-40,40H112a40,40,0,0,1-40-40v-8a41.74,41.74,0,0,1,6.9-22.48A8,8,0,0,0,80,73.83a43.81,43.81,0,0,1,.79-33.58,43.88,43.88,0,0,1,32.32,20.06A8,8,0,0,0,119.82,64h32.35a8,8,0,0,0,6.74-3.69,43.87,43.87,0,0,1,32.32-20.06A43.81,43.81,0,0,1,192,73.83a8.09,8.09,0,0,0,1,7.65A41.72,41.72,0,0,1,200,104Z"
                    ></path>
                  </svg>
                </div>
              </button>
              <button
                class="flex max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 bg-[#293542] text-white gap-2 text-sm font-bold leading-normal tracking-[0.015em] min-w-0 px-2.5"
              >
                <div
                  class="text-white"
                  data-icon="LinkedinLogo"
                  data-size="20px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M216,24H40A16,16,0,0,0,24,40V216a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V40A16,16,0,0,0,216,24Zm0,192H40V40H216V216ZM96,112v64a8,8,0,0,1-16,0V112a8,8,0,0,1,16,0Zm88,28v36a8,8,0,0,1-16,0V140a20,20,0,0,0-40,0v36a8,8,0,0,1-16,0V112a8,8,0,0,1,15.79-1.78A36,36,0,0,1,184,140ZM100,84A12,12,0,1,1,88,72,12,12,0,0,1,100,84Z"
                    ></path>
                  </svg>
                </div>
              </button>
            </div>
          </div>
        </header>
        <div class="px-40 flex flex-1 justify-center py-5">
          <div class="layout-content-container flex flex-col max-w-[960px] flex-1">
            <div class="w-full px-4">
              <div class="max-w-[600px] mx-auto">
                <div class="flex w-full flex-col gap-4 @[520px]:flex-row @[520px]:justify-center @[520px]:items-center">
                  <div class="flex gap-4 items-center">
                    <div
                    class="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-32 w-32 mt-20"
                    style="background-image: url('images/LogoNew.png'); background-size: 210%;"
                    ></div>
                    <div class="flex flex-col justify-center">
                      <p class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em]">
                        Thomas van Rossum
                      </p>
                      <p class="text-[#9badc0] text-base font-normal leading-normal">
                        MERN &amp; AI Automation Developer
                      </p>
                      <p class="text-[#9badc0] text-base font-normal leading-normal">
                        Experienced developer specializing in MERN stack and AI automation solutions.
                      </p>
                  </div>
                </div>
              </div>
            </div>
            <h2
              class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
            >
              About Me
            </h2>
            <p
              class="text-white text-base font-normal leading-normal pb-3 pt-1 px-4"
            >
              I am a passionate and results-driven developer with a strong
              background in building robust web applications using the MERN stack
              (MongoDB, Express.js, React, Node.js). My expertise extends to
              creating AI-powered automations that streamline processes and
              enhance efficiency. I thrive on tackling complex challenges and
              delivering innovative solutions that meet and exceed client
              expectations.
            </p>
            <h2
              class="text-white text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5"
            >
              Background
            </h2>
            <div class="grid grid-cols-[40px_1fr] gap-x-2 px-4">
              <div class="flex flex-col items-center gap-1 pt-3">
                <div
                  class="text-white"
                  data-icon="GraduationCap"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24px"
                    height="24px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M251.76,88.94l-120-64a8,8,0,0,0-7.52,0l-120,64a8,8,0,0,0,0,14.12L32,117.87v48.42a15.91,15.91,0,0,0,4.06,10.65C49.16,191.53,78.51,216,128,216a130,130,0,0,0,48-8.76V240a8,8,0,0,0,16,0V199.51a115.63,115.63,0,0,0,27.94-22.57A15.91,15.91,0,0,0,224,166.29V117.87l27.76-14.81a8,8,0,0,0,0-14.12ZM128,200c-43.27,0-68.72-21.14-80-33.71V126.4l76.24,40.66a8,8,0,0,0,7.52,0L176,143.47v46.34C163.4,195.69,147.52,200,128,200Zm80-33.75a97.83,97.83,0,0,1-16,14.25V134.93l16-8.53ZM188,118.94l-.22-.13-56-29.87a8,8,0,0,0-7.52,14.12L171,128l-43,22.93L25,96,128,41.07,231,96Z"
                    ></path>
                  </svg>
                </div>
                <div class="w-[1.5px] bg-[#3b4c5e] h-2 grow"></div>
              </div>
              <div class="flex flex-1 flex-col py-3">
                <p class="text-white text-base font-medium leading-normal">
                  Education
                </p>
                <p class="text-[#9badc0] text-base font-normal leading-normal">
                  Master of Science in Computer Science, University of Amsterdam
                </p>
              </div>
              <div class="flex flex-col items-center gap-1">
                <div class="w-[1.5px] bg-[#3b4c5e] h-2"></div>
                <div
                  class="text-white"
                  data-icon="Briefcase"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24px"
                    height="24px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M216,56H176V48a24,24,0,0,0-24-24H104A24,24,0,0,0,80,48v8H40A16,16,0,0,0,24,72V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V72A16,16,0,0,0,216,56ZM96,48a8,8,0,0,1,8-8h48a8,8,0,0,1,8,8v8H96ZM216,72v41.61A184,184,0,0,1,128,136a184.07,184.07,0,0,1-88-22.38V72Zm0,128H40V131.64A200.19,200.19,0,0,0,128,152a200.25,200.25,0,0,0,88-20.37V200ZM104,112a8,8,0,0,1,8-8h32a8,8,0,0,1,0,16H112A8,8,0,0,1,104,112Z"
                    ></path>
                  </svg>
                </div>
                <div class="w-[1.5px] bg-[#3b4c5e] h-2 grow"></div>
              </div>
              <div class="flex flex-1 flex-col py-3">
                <p class="text-white text-base font-medium leading-normal">
                  Work History
                </p>
                <p class="text-[#9badc0] text-base font-normal leading-normal">
                  Senior Developer at Tech Solutions Inc.
                </p>
              </div>
              <div class="flex flex-col items-center gap-1 pb-3">
                <div class="w-[1.5px] bg-[#3b4c5e] h-2"></div>
                <div
                  class="text-white"
                  data-icon="Code"
                  data-size="24px"
                  data-weight="regular"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24px"
                    height="24px"
                    fill="currentColor"
                    viewBox="0 0 256 256"
                  >
                    <path
                      d="M69.12,94.15,28.5,128l40.62,33.85a8,8,0,1,1-10.24,12.29l-48-40a8,8,0,0,1,0-12.29l48-40a8,8,0,0,1,10.24,12.3Zm176,27.7-48-40a8,8,0,1,0-10.24,12.3L227.5,128l-40.62,33.85a8,8,0,1,0,10.24,12.29l48-40a8,8,0,0,0,0-12.29ZM162.73,32.48a8,8,0,0,0-10.25,4.79l-64,176a8,8,0,0,0,4.79,10.26A8.14,8.14,0,0,0,96,224a8,8,0,0,0,7.52-5.27l64-176A8,8,0,0,0,162.73,32.48Z"
                    ></path>
                  </svg>
                </div>
              </div>
              <div class="flex flex-1 flex-col py-3">
                <p class="text-white text-base font-medium leading-normal">
                  Notable Projects
                </p>
                <p class="text-[#9badc0] text-base font-normal leading-normal">
                  Developed an AI-powered automation tool for data analysis
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
