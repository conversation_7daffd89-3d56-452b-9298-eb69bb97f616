<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> - MERN Stack Developer & AI Automation Expert</title>
    <meta name="description" content="<PERSON> is a MERN stack developer and AI automation specialist creating innovative solutions for modern businesses." />
    <link rel="stylesheet" href="main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
</head>
<body class="bg-background text-text-primary">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <nav class="container-max section-padding py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">TV</span>
                    </div>
                    <span class="text-xl font-semibold"><PERSON> van <PERSON>um</span>
                </div>

                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-accent font-medium">Home</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary">Get In Touch</a>
                </div>

                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-surface transition-smooth" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div class="md:hidden mt-4 pb-4 border-t border-border pt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-accent font-medium">Home</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary w-full text-center">Get In Touch</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="section-padding">
        <div class="container-max">
            <div class="relative">
                <div class="relative flex min-h-[480px] flex-col gap-6 bg-cover bg-center bg-no-repeat rounded-xl items-start justify-end px-4 pb-10 lg:px-10"
                     style='background-image: linear-gradient(rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%), url("https://lh3.googleusercontent.com/aida-public/AB6AXuCe8adstEI8IYilx9n5qtGPTn971rf2EzdR29Of-Xbajy01VQKR_qfFbTLVqyddXDGxB4YI4_hv6Ae84JfVEbOmAK-C0FCwG_gVPv0Sytr31tv5VgFqG3XpZF1wLAh7PSwPDRsRtTVeEGyl2aN29ziC4AiVxDlBhq17aVggRVOaEZ_FfsFsMswO8nFkjm4-8bgE9RuVQi2U38jDG268f0_feFcw8yNGTvKoU5cTnOqF0TJ-F4Z-ifKYhFcEeinCEF5YoevCGBxvSAsv");'>

                    <!-- Profile image overlay -->
                    <div class="flex items-center justify-end h-full w-full z-10">
                        <div class="rounded-full w-[21rem] h-[21rem] lg:w-[26.25rem] lg:h-[26.25rem] border-4 border-[#101923] shadow-lg overflow-hidden flex items-center justify-center">
                            <img src="images/LogoNew.png" alt="Thomas van Rossum Profile" class="w-full h-full object-cover" style="transform: scale(1.22);" />
                        </div>
                    </div>

                    <!-- Text content -->
                    <div class="absolute top-10 left-10 z-10 flex flex-col gap-2 text-left">
                        <h1 class="text-white text-4xl lg:text-5xl font-black leading-tight tracking-tight">
                            MERN &<br />
                            Automation Expert
                        </h1>
                        <h2 class="text-white text-sm lg:text-base font-normal leading-normal">
                            Transforming ideas into reality with cutting-edge <br />
                            web solutions and AI-powered automations.
                        </h2>
                        <a href="projects.html" class="mt-4 w-fit px-5 py-2 rounded-full bg-accent text-white text-sm font-medium hover:bg-blue-600 transition-smooth">
                            Check out some of my latest projects!
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Projects Section -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Featured Projects</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Project 1 -->
                <div class="card hover:shadow-hover transition-smooth">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold mb-3">PO Management Dashboard</h3>
                            <p class="text-text-secondary mb-4">
                                A comprehensive dashboard for managing purchase orders, streamlining workflows, and enhancing efficiency. Built with modern technologies for optimal performance.
                            </p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React</span>
                                <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                                <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                                <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Socket.io</span>
                            </div>
                            <a href="projects.html" class="btn-primary text-sm">View Project</a>
                        </div>
                        <div class="w-full lg:w-48 h-32 lg:h-auto">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="PO Management Dashboard" class="w-full h-full object-cover rounded-lg" />
                        </div>
                    </div>
                </div>

                <!-- Project 2 -->
                <div class="card hover:shadow-hover transition-smooth">
                    <div class="flex flex-col lg:flex-row gap-6">
                        <div class="flex-1">
                            <h3 class="text-xl font-semibold mb-3">AI Contract Reviewer</h3>
                            <p class="text-text-secondary mb-4">
                                An AI-powered tool that automates contract reviews, identifies potential risks, and ensures compliance. Leverages advanced NLP for intelligent analysis.
                            </p>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                                <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">OpenAI</span>
                                <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">FastAPI</span>
                                <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">NLP</span>
                            </div>
                            <a href="projects.html" class="btn-primary text-sm">View Project</a>
                        </div>
                        <div class="w-full lg:w-48 h-32 lg:h-auto">
                            <img src="https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="AI Contract Reviewer" class="w-full h-full object-cover rounded-lg" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-8">
                <a href="projects.html" class="btn-secondary">View All Projects</a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section-padding">
        <div class="container-max">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl font-bold mb-6">About Me</h2>
                    <p class="text-text-secondary mb-6 leading-relaxed">
                        I am a passionate and results-driven developer with a strong background in building robust web applications using the MERN stack (MongoDB, Express.js, React, Node.js). My expertise extends to creating AI-powered automations that streamline processes and enhance efficiency.
                    </p>
                    <p class="text-text-secondary mb-6 leading-relaxed">
                        I thrive on tackling complex challenges and delivering innovative solutions that meet and exceed client expectations. With a focus on modern technologies and best practices, I help businesses transform their ideas into reality.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="about.html" class="btn-primary">Learn More About Me</a>
                        <a href="contact.html" class="btn-secondary">Get In Touch</a>
                    </div>
                </div>
                <div class="flex justify-center">
                    <div class="rounded-full w-80 h-80 border-4 border-[#101923] shadow-lg overflow-hidden flex items-center justify-center">
                        <img src="images/LogoNew.png" alt="Thomas van Rossum" class="w-full h-full object-cover" style="transform: scale(1.22);" />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <h2 class="text-3xl font-bold mb-8 text-center">Core Technologies</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-blue-400 font-bold text-lg">R</span>
                    </div>
                    <h3 class="font-semibold text-sm">React</h3>
                </div>
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-green-400 font-bold text-lg">N</span>
                    </div>
                    <h3 class="font-semibold text-sm">Node.js</h3>
                </div>
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-yellow-400 font-bold text-lg">M</span>
                    </div>
                    <h3 class="font-semibold text-sm">MongoDB</h3>
                </div>
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-purple-400 font-bold text-lg">E</span>
                    </div>
                    <h3 class="font-semibold text-sm">Express</h3>
                </div>
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-red-400 font-bold text-lg">AI</span>
                    </div>
                    <h3 class="font-semibold text-sm">AI/ML</h3>
                </div>
                <div class="card text-center hover:shadow-hover transition-smooth">
                    <div class="w-12 h-12 bg-orange-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <span class="text-orange-400 font-bold text-lg">A</span>
                    </div>
                    <h3 class="font-semibold text-sm">Automation</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section-padding bg-gradient-to-br from-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-4xl font-bold mb-6">Ready to Start Your Project?</h2>
                <p class="text-xl text-text-secondary mb-8">
                    Let's discuss how I can help bring your vision to life with cutting-edge technology and proven expertise.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.html" class="btn-primary">
                        Start a Conversation
                    </a>
                    <a href="projects.html" class="btn-secondary">
                        View My Work
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary border-t border-border">
        <div class="container-max section-padding py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">TV</span>
                        </div>
                        <span class="text-lg font-semibold">Thomas van Rossum</span>
                    </div>
                    <p class="text-text-secondary mb-4">
                        MERN Stack Developer & AI Automation Specialist creating innovative solutions for modern businesses.
                    </p>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Navigation</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="index.html" class="hover:text-accent transition-smooth">Home</a></li>
                        <li><a href="about.html" class="hover:text-accent transition-smooth">About</a></li>
                        <li><a href="projects.html" class="hover:text-accent transition-smooth">Projects</a></li>
                        <li><a href="blog.html" class="hover:text-accent transition-smooth">Blog</a></li>
                        <li><a href="contact.html" class="hover:text-accent transition-smooth">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold mb-4">Services</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="#" class="hover:text-accent transition-smooth">MERN Development</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">AI Automation</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Consulting</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Technical Audits</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-border mt-8 pt-8 text-center text-text-secondary">
                <p>&copy; 2025 Thomas van Rossum. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>
