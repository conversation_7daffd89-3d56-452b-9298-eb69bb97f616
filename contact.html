<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Contact <PERSON> - Get In Touch</title>
    <meta name="description" content="Get in touch with <PERSON> for your MERN stack development and AI automation projects. Let's discuss your ideas and bring them to life." />
    <link rel="stylesheet" href="main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
</head>
<body class="bg-background text-text-primary">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <nav class="container-max section-padding py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">TV</span>
                    </div>
                    <span class="text-xl font-semibold"><PERSON> van <PERSON>um</span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="text-accent font-medium">Contact</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-surface transition-smooth" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Menu -->
            <div class="md:hidden mt-4 pb-4 border-t border-border pt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="about.html" class="text-text-secondary hover:text-text-primary transition-smooth">About</a>
                    <a href="projects.html" class="text-text-secondary hover:text-text-primary transition-smooth">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="text-accent font-medium">Contact</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-background via-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    Let's <span class="text-gradient">Work Together</span>
                </h1>
                <p class="text-xl text-text-secondary mb-8 leading-relaxed">
                    Ready to bring your ideas to life? I'd love to discuss your project and explore how we can create something amazing together.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Form Section -->
    <section class="section-padding">
        <div class="container-max">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div class="card">
                    <h2 class="text-2xl font-bold mb-6">Send Me a Message</h2>
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="firstName" class="block text-sm font-medium mb-2">First Name</label>
                                <input type="text" id="firstName" name="firstName" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="John" required />
                            </div>
                            <div>
                                <label for="lastName" class="block text-sm font-medium mb-2">Last Name</label>
                                <input type="text" id="lastName" name="lastName" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="Doe" required />
                            </div>
                        </div>
                        
                        <div>
                            <label for="email" class="block text-sm font-medium mb-2">Email Address</label>
                            <input type="email" id="email" name="email" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="<EMAIL>" required />
                        </div>
                        
                        <div>
                            <label for="company" class="block text-sm font-medium mb-2">Company (Optional)</label>
                            <input type="text" id="company" name="company" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent" placeholder="Your Company" />
                        </div>
                        
                        <div>
                            <label for="projectType" class="block text-sm font-medium mb-2">Project Type</label>
                            <select id="projectType" name="projectType" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent">
                                <option value="">Select a project type</option>
                                <option value="web-development">Web Development</option>
                                <option value="ai-automation">AI Automation</option>
                                <option value="mern-stack">MERN Stack Application</option>
                                <option value="consulting">Consulting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="budget" class="block text-sm font-medium mb-2">Budget Range</label>
                            <select id="budget" name="budget" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent">
                                <option value="">Select budget range</option>
                                <option value="5k-10k">$5,000 - $10,000</option>
                                <option value="10k-25k">$10,000 - $25,000</option>
                                <option value="25k-50k">$25,000 - $50,000</option>
                                <option value="50k+">$50,000+</option>
                                <option value="discuss">Let's discuss</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="message" class="block text-sm font-medium mb-2">Project Details</label>
                            <textarea id="message" name="message" rows="6" class="w-full px-4 py-3 rounded-lg bg-surface border border-border text-text-primary placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-accent focus:border-transparent resize-none" placeholder="Tell me about your project, goals, timeline, and any specific requirements..." required></textarea>
                        </div>
                        
                        <button type="submit" class="btn-primary w-full py-4">
                            Send Message
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="space-y-8">
                    <div class="card">
                        <h3 class="text-xl font-semibold mb-4">Get In Touch</h3>
                        <p class="text-text-secondary mb-6">
                            I'm always excited to discuss new projects and opportunities. Whether you have a specific idea in mind or just want to explore possibilities, I'd love to hear from you.
                        </p>
                        
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium">Email</div>
                                    <div class="text-text-secondary"><EMAIL></div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium">Response Time</div>
                                    <div class="text-text-secondary">Within 24 hours</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="font-medium">Location</div>
                                    <div class="text-text-secondary">Available Worldwide</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-semibold mb-4">Connect on Social</h3>
                        <div class="flex space-x-4">
                            <a href="#" class="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center hover:bg-accent/30 transition-smooth">
                                <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                </svg>
                            </a>
                            <a href="#" class="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center hover:bg-accent/30 transition-smooth">
                                <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                            <a href="#" class="w-12 h-12 bg-accent/20 rounded-lg flex items-center justify-center hover:bg-accent/30 transition-smooth">
                                <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div class="card">
                        <h3 class="text-xl font-semibold mb-4">What to Expect</h3>
                        <ul class="space-y-3 text-text-secondary">
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-success mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <span>Initial consultation within 24 hours</span>
                            </li>
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-success mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <span>Detailed project proposal and timeline</span>
                            </li>
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-success mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <span>Regular updates throughout development</span>
                            </li>
                            <li class="flex items-start space-x-3">
                                <svg class="w-5 h-5 text-success mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <span>Post-launch support and maintenance</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-primary border-t border-border">
        <div class="container-max section-padding py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-accent rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold">TV</span>
                        </div>
                        <span class="text-lg font-semibold">Thomas van Rossum</span>
                    </div>
                    <p class="text-text-secondary mb-4">
                        MERN Stack Developer & AI Automation Specialist creating innovative solutions for modern businesses.
                    </p>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Navigation</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="index.html" class="hover:text-accent transition-smooth">Home</a></li>
                        <li><a href="about.html" class="hover:text-accent transition-smooth">About</a></li>
                        <li><a href="projects.html" class="hover:text-accent transition-smooth">Projects</a></li>
                        <li><a href="blog.html" class="hover:text-accent transition-smooth">Blog</a></li>
                        <li><a href="contact.html" class="hover:text-accent transition-smooth">Contact</a></li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-4">Services</h3>
                    <ul class="space-y-2 text-text-secondary">
                        <li><a href="#" class="hover:text-accent transition-smooth">MERN Development</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">AI Automation</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Consulting</a></li>
                        <li><a href="#" class="hover:text-accent transition-smooth">Technical Audits</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-border mt-8 pt-8 text-center text-text-secondary">
                <p>&copy; 2025 Thomas van Rossum. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });
    </script>
</body>
</html>
