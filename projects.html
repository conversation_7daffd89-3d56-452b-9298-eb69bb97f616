<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Projects Portfolio & Case Studies - <PERSON></title>
    <meta name="description" content="Explore <PERSON>'s comprehensive portfolio of MERN stack and AI automation projects with detailed case studies, live demos, and measurable results." />
    <link rel="stylesheet" href="main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<script type="module" src="https://static.rocket.new/rocket-web.js?_cfg=https%3A%2F%2Fthomasvan2028back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5"></script>
</head>
<body class="bg-background text-text-primary">
    <!-- Navigation Header -->
    <header class="sticky top-0 z-50 bg-background/95 backdrop-blur-sm border-b border-border">
        <nav class="container-max section-padding py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">TV</span>
                    </div>
                    <span class="text-xl font-semibold">Thomas van Rossum</span>
                </div>
                
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="projects.html" class="text-accent font-medium">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary">Get In Touch</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <button class="md:hidden p-2 rounded-lg hover:bg-surface transition-smooth" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Menu -->
            <div class="md:hidden mt-4 pb-4 border-t border-border pt-4 hidden" id="mobile-menu">
                <div class="flex flex-col space-y-4">
                    <a href="index.html" class="text-text-secondary hover:text-text-primary transition-smooth">Home</a>
                    <a href="projects.html" class="text-accent font-medium">Projects</a>
                    <a href="blog.html" class="text-text-secondary hover:text-text-primary transition-smooth">Blog</a>
                    <a href="contact.html" class="btn-primary w-full text-center">Get In Touch</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="section-padding bg-gradient-to-br from-background via-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-6xl font-bold mb-6">
                    Projects & <span class="text-gradient">Case Studies</span>
                </h1>
                <p class="text-xl text-text-secondary mb-8 leading-relaxed">
                    Explore my comprehensive portfolio of MERN stack applications and AI automation solutions. 
                    Each project showcases technical excellence, innovative problem-solving, and measurable business impact.
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-12">
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">15+ Projects Completed</span>
                    </div>
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">98% Client Satisfaction</span>
                    </div>
                    <div class="bg-surface px-4 py-2 rounded-full border border-border">
                        <span class="text-sm font-medium">$2M+ Revenue Generated</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="section-padding bg-surface/50">
        <div class="container-max">
            <div class="flex flex-wrap items-center justify-between mb-8">
                <h2 class="text-2xl font-semibold mb-4 md:mb-0">Filter Projects</h2>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center space-x-2 text-text-secondary hover:text-text-primary transition-smooth" id="reset-filters">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        <span>Reset Filters</span>
                    </button>
                    <button class="flex items-center space-x-2 text-text-secondary hover:text-text-primary transition-smooth">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        <span>Search</span>
                    </button>
                </div>
            </div>
            
            <div class="flex flex-wrap gap-3 mb-8">
                <button class="filter-btn active bg-accent text-white px-4 py-2 rounded-lg font-medium transition-smooth" data-filter="all">
                    All Projects
                </button>
                <button class="filter-btn bg-surface hover:bg-slate-700 text-text-primary px-4 py-2 rounded-lg font-medium transition-smooth border border-border" data-filter="mern">
                    MERN Stack
                </button>
                <button class="filter-btn bg-surface hover:bg-slate-700 text-text-primary px-4 py-2 rounded-lg font-medium transition-smooth border border-border" data-filter="ai">
                    AI Automation
                </button>
                <button class="filter-btn bg-surface hover:bg-slate-700 text-text-primary px-4 py-2 rounded-lg font-medium transition-smooth border border-border" data-filter="ecommerce">
                    E-commerce
                </button>
                <button class="filter-btn bg-surface hover:bg-slate-700 text-text-primary px-4 py-2 rounded-lg font-medium transition-smooth border border-border" data-filter="saas">
                    SaaS
                </button>
                <button class="filter-btn bg-surface hover:bg-slate-700 text-text-primary px-4 py-2 rounded-lg font-medium transition-smooth border border-border" data-filter="mobile">
                    Mobile Apps
                </button>
            </div>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="section-padding">
        <div class="container-max">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="projects-grid">
                
                <!-- Project Card 1 - E-commerce Platform -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="mern ecommerce">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="E-commerce Platform Dashboard" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.pexels.com/photos/230544/pexels-photo-230544.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-success text-white px-2 py-1 rounded-full text-xs font-medium">Live</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">NextGen E-commerce Platform</h3>
                            <p class="text-text-secondary text-sm mb-3">Full-stack e-commerce solution with advanced inventory management and AI-powered recommendations.</p>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Express</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">+40%</div>
                                <div class="text-xs text-text-secondary">Performance</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">+65%</div>
                                <div class="text-xs text-text-secondary">User Engagement</div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('ecommerce')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 2 - AI Chatbot -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="ai saas">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="AI Chatbot Interface" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-warning text-white px-2 py-1 rounded-full text-xs font-medium">Beta</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Intelligent Customer Support Bot</h3>
                            <p class="text-text-secondary text-sm mb-3">AI-powered chatbot with natural language processing and automated workflow integration.</p>
                        </div>
                        
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">TensorFlow</span>
                            <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">OpenAI</span>
                            <span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs">FastAPI</span>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">-75%</div>
                                <div class="text-xs text-text-secondary">Response Time</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">92%</div>
                                <div class="text-xs text-text-secondary">Accuracy Rate</div>
                            </div>
                        </div>
                        
                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('ai-chatbot')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 3 - Mobile App -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="mobile mern">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pixabay.com/photo/2016/12/09/11/33/smartphone-1894723_1280.jpg" alt="Mobile App Interface" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-success text-white px-2 py-1 rounded-full text-xs font-medium">Live</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">FinTech Mobile Application</h3>
                            <p class="text-text-secondary text-sm mb-3">Cross-platform mobile app for financial management with real-time analytics and secure transactions.</p>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">React Native</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Node.js</span>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">PostgreSQL</span>
                            <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">Redis</span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">50K+</div>
                                <div class="text-xs text-text-secondary">Downloads</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">4.8★</div>
                                <div class="text-xs text-text-secondary">App Rating</div>
                            </div>
                        </div>

                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('fintech-mobile')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 4 - SaaS Platform -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="saas mern">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3" alt="SaaS Dashboard Analytics" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.pexels.com/photos/590022/pexels-photo-590022.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-success text-white px-2 py-1 rounded-full text-xs font-medium">Live</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Project Management SaaS</h3>
                            <p class="text-text-secondary text-sm mb-3">Comprehensive project management platform with team collaboration and advanced reporting features.</p>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Next.js</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Express</span>
                            <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">MongoDB</span>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Socket.io</span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">+85%</div>
                                <div class="text-xs text-text-secondary">Team Productivity</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">1000+</div>
                                <div class="text-xs text-text-secondary">Active Users</div>
                            </div>
                        </div>

                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('project-management')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 5 - AI Automation -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="ai">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pexels.com/photos/8386434/pexels-photo-8386434.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="AI Workflow Automation" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-success text-white px-2 py-1 rounded-full text-xs font-medium">Live</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Workflow Automation Suite</h3>
                            <p class="text-text-secondary text-sm mb-3">AI-powered workflow automation platform that streamlines business processes and reduces manual tasks.</p>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Python</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Celery</span>
                            <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">OpenAI</span>
                            <span class="bg-orange-500/20 text-orange-400 px-2 py-1 rounded text-xs">Docker</span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">-60%</div>
                                <div class="text-xs text-text-secondary">Manual Tasks</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">$500K</div>
                                <div class="text-xs text-text-secondary">Cost Savings</div>
                            </div>
                        </div>

                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('workflow-automation')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.30.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Project Card 6 - E-commerce Mobile -->
                <div class="project-card card hover:shadow-hover transition-smooth cursor-pointer" data-category="ecommerce mobile">
                    <div class="relative overflow-hidden rounded-lg mb-4">
                        <img src="https://images.pixabay.com/photo/2017/03/13/17/26/ecommerce-2140603_1280.jpg" alt="E-commerce Mobile App" class="w-full h-48 object-cover transition-transform duration-300 hover:scale-105" loading="lazy" onerror="this.src='https://images.unsplash.com/photo-1563013544-824ae1b704d3?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3'; this.onerror=null;" />
                        <div class="absolute top-4 right-4">
                            <span class="bg-success text-white px-2 py-1 rounded-full text-xs font-medium">Live</span>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <h3 class="text-xl font-semibold mb-2">Fashion E-commerce App</h3>
                            <p class="text-text-secondary text-sm mb-3">Mobile-first e-commerce platform with AR try-on features and personalized shopping experiences.</p>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-blue-500/20 text-blue-400 px-2 py-1 rounded text-xs">Flutter</span>
                            <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Firebase</span>
                            <span class="bg-purple-500/20 text-purple-400 px-2 py-1 rounded text-xs">Stripe</span>
                            <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">ARCore</span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">+120%</div>
                                <div class="text-xs text-text-secondary">Conversion Rate</div>
                            </div>
                            <div class="text-center">
                                <div class="text-lg font-semibold text-success">100K+</div>
                                <div class="text-xs text-text-secondary">Monthly Users</div>
                            </div>
                        </div>

                        <div class="flex space-x-3">
                            <button class="btn-primary flex-1 text-sm py-2" onclick="openCaseStudy('fashion-ecommerce')">
                                View Case Study
                            </button>
                            <a href="#" class="btn-secondary px-4 py-2 text-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="section-padding bg-gradient-to-br from-primary to-secondary">
        <div class="container-max">
            <div class="text-center max-w-3xl mx-auto">
                <h2 class="text-4xl font-bold mb-6">Ready to Start Your Project?</h2>
                <p class="text-xl text-text-secondary mb-8">
                    Let's discuss how I can help bring your vision to life with cutting-edge technology and proven expertise.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="mailto:<EMAIL>" class="btn-primary">
                        Start a Conversation
                    </a>
                    <a href="blog.html" class="btn-secondary">
                        Read Technical Blog
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Filter functionality
        const filterButtons = document.querySelectorAll('.filter-btn');
        const projectCards = document.querySelectorAll('.project-card');

        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                
                // Update active button
                filterButtons.forEach(btn => {
                    btn.classList.remove('active', 'bg-accent', 'text-white');
                    btn.classList.add('bg-surface', 'hover:bg-slate-700', 'text-text-primary');
                });
                this.classList.add('active', 'bg-accent', 'text-white');
                this.classList.remove('bg-surface', 'hover:bg-slate-700', 'text-text-primary');
                
                // Filter projects
                projectCards.forEach(card => {
                    const categories = card.getAttribute('data-category').split(' ');
                    if (filter === 'all' || categories.includes(filter)) {
                        card.style.display = 'block';
                        card.classList.add('animate-fade-in');
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });

        // Reset filters
        document.getElementById('reset-filters').addEventListener('click', function() {
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-accent', 'text-white');
                btn.classList.add('bg-surface', 'hover:bg-slate-700', 'text-text-primary');
            });
            filterButtons[0].classList.add('active', 'bg-accent', 'text-white');
            filterButtons[0].classList.remove('bg-surface', 'hover:bg-slate-700', 'text-text-primary');
            
            projectCards.forEach(card => {
                card.style.display = 'block';
            });
        });

        // Case study modal functionality
        function openCaseStudy(projectId) {
            // This will be implemented when we add the modal
            console.log('Opening case study for:', projectId);
        }
    </script>
</body>
</html>
